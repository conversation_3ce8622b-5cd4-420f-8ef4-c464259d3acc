#!/usr/bin/env python3

import requests
import time

def test_performance_fix():
    """Test that the performance summary works with baseline-only scans"""
    base_url = "http://127.0.0.1:8000/api"
    
    print("🧪 Testing Performance Summary Fix...")
    
    # Step 1: Create KvK
    print("\n1. Creating KvK...")
    timestamp = int(time.time())
    kvk_data = {
        'name': f'Test KvK {timestamp}',
        'start_date': '2024-01-01T00:00:00',
        'season': 1,
        'status': 'upcoming'
    }
    
    kvk_response = requests.post(f"{base_url}/kvks/", json=kvk_data)
    print(f"   Status: {kvk_response.status_code}")
    
    if kvk_response.status_code != 201:
        print(f"   ❌ KvK creation failed: {kvk_response.text}")
        return
    
    kvk = kvk_response.json()
    kvk_id = kvk['id']
    print(f"   ✅ Created KvK: {kvk['name']} (ID: {kvk_id}, Status: {kvk['status']})")
    
    # Step 2: Create a simple baseline scan manually using the API
    print(f"\n2. Creating baseline scan for KvK {kvk_id}...")
    
    # Create a simple CSV content for testing
    csv_content = """player_name,power,total_kill_points,dead_troops,alliance,governor_id
Test Player 1,50000000,1000000,500000,[TEST]Test Alliance,12345
Test Player 2,45000000,800000,400000,[TEST]Test Alliance,12346
Test Player 3,40000000,600000,300000,[TEST]Test Alliance,12347"""
    
    # Save to a temporary file
    with open('test_baseline.csv', 'w') as f:
        f.write(csv_content)
    
    # Upload the scan
    with open('test_baseline.csv', 'rb') as f:
        files = {'file': f}
        url = f"{base_url}/scans/upload?scan_name=Test Baseline&is_baseline=true&kvk_id={kvk_id}"
        
        scan_response = requests.post(url, files=files)
        print(f"   Status: {scan_response.status_code}")
        
        if scan_response.status_code != 200:
            print(f"   ❌ Scan upload failed: {scan_response.text}")
            return
        
        scan = scan_response.json()
        print(f"   ✅ Uploaded scan: {scan['name']} (ID: {scan['id']}, KvK ID: {scan.get('kvk_id', 'None')})")
    
    # Step 3: Test performance summary with baseline-only data
    print(f"\n3. Testing performance summary for KvK {kvk_id}...")
    perf_response = requests.get(f"{base_url}/reports/kvk/performance_summary?kvk_id={kvk_id}&limit=5")
    print(f"   Status: {perf_response.status_code}")
    
    if perf_response.status_code == 200:
        data = perf_response.json()
        print("   ✅ Performance summary successful!")
        print(f"   📊 Total players: {data['summary_stats']['total_players']}")
        print(f"   📅 Baseline scan: {data['scan_period']['baseline_scan_name']}")
        print(f"   📅 Latest scan: {data['scan_period']['latest_scan_name']}")
        print(f"   👥 Sample players returned: {len(data['performance_data'])}")
        
        # Verify baseline-only logic
        if data['scan_period']['baseline_scan_id'] == data['scan_period']['latest_scan_id']:
            print("   ✅ Correctly detected baseline-only scenario")
            
            if data['performance_data']:
                player = data['performance_data'][0]
                print(f"   🎯 Sample player: {player['player_name']}")
                print(f"      - Power: {player['current_power']:,}")
                print(f"      - Kill Points: {player['current_kp']:,}")
                print(f"      - Dead Troops: {player['current_dead']:,}")
                print(f"      - KP Delta: {player['kp_delta']} (should be 0 for baseline)")
                print(f"      - Power Delta: {player['power_delta']} (should be 0 for baseline)")
                
                # Verify all deltas are 0 for baseline-only
                all_zero_deltas = all(
                    p['kp_delta'] == 0 and p['power_delta'] == 0 and p['dead_delta'] == 0 
                    for p in data['performance_data']
                )
                
                if all_zero_deltas:
                    print("   ✅ All deltas are correctly set to 0 for baseline-only scenario")
                else:
                    print("   ❌ Some deltas are not 0 - baseline logic may have issues")
            else:
                print("   ❌ No performance data returned")
        else:
            print("   ❌ Did not detect baseline-only scenario correctly")
    else:
        print(f"   ❌ Performance summary failed: {perf_response.text}")
        return
    
    # Step 4: Test active KvK endpoint
    print(f"\n4. Testing active KvK endpoint...")
    active_response = requests.get(f"{base_url}/kvks/active")
    print(f"   Status: {active_response.status_code}")
    
    if active_response.status_code == 200:
        active_kvk = active_response.json()
        print(f"   ✅ Active KvK: {active_kvk['name']} (ID: {active_kvk['id']})")
    else:
        print(f"   ❌ No active KvK: {active_response.text}")
    
    # Cleanup
    import os
    if os.path.exists('test_baseline.csv'):
        os.remove('test_baseline.csv')
    
    print("\n🎉 Performance fix test completed!")

if __name__ == "__main__":
    test_performance_fix()
