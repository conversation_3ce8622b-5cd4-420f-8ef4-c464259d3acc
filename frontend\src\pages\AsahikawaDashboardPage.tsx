import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import StatCard from '../components/StatCard';
import Leaderboard from '../components/Leaderboard';
import ChartCard from '../components/ChartCard';
import {
  mockKingdomData,
  topPowerPlayers,
  topKillsPlayers
} from '../services/mockData';

// Icons for stat cards
const PowerIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
  </svg>
);

const KillsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const TroopsIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
  </svg>
);

const PlayersIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
);

const AsahikawaDashboardPage: React.FC = () => {
  const { theme } = useTheme();

  // Format large numbers
  const formatNumber = (num: number): string => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)}B`;
    }
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <div className={`p-4 sm:p-6 md:px-8 transition-colors duration-300 ease-in-out ${theme === 'light' ? 'bg-slate-50 text-slate-900' : 'bg-gray-900 text-gray-100'}`}>
      <header className="mb-8 flex flex-col md:flex-row justify-between items-start md:items-center">
        <h1 className={`text-3xl sm:text-4xl font-bold ${theme === 'light' ? 'text-blue-600' : 'text-blue-400'} mb-4 md:mb-0`}>
          Asahikawa 2358 Dashboard
        </h1>
        <div className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
          Last updated: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}
        </div>
      </header>

      {/* Summary Cards */}
      <section className="mb-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Power"
          value={mockKingdomData.totalPower}
          icon={<PowerIcon />}
          formatter={formatNumber}
        />
        <StatCard
          title="Kill Points"
          value={mockKingdomData.totalKillPoints}
          icon={<KillsIcon />}
          formatter={formatNumber}
        />
        <StatCard
          title="Dead Troops"
          value={mockKingdomData.totalDeadTroops}
          icon={<TroopsIcon />}
          formatter={formatNumber}
        />
        <StatCard
          title="Active Players"
          value={`${mockKingdomData.activePlayers}/${mockKingdomData.totalPlayers}`}
          icon={<PlayersIcon />}
        />
      </section>

      {/* Leaderboards */}
      <section className="mb-8">
        <h2 className={`text-2xl font-semibold mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Leaderboards
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Leaderboard
            title="Top Players by Power"
            players={topPowerPlayers}
            valueKey="power"
            valueFormatter={formatNumber}
          />
          <Leaderboard
            title="Top Players by Kills"
            players={topKillsPlayers}
            valueKey="killPoints"
            valueFormatter={formatNumber}
          />
        </div>
      </section>

      {/* Charts */}
      <section className="mb-8">
        <h2 className={`text-2xl font-semibold mb-4 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
          Kingdom Statistics
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <ChartCard
            title="Power by Alliance"
            data={mockKingdomData.powerByAlliance}
            type="bar"
          />
          <ChartCard
            title="Kills by Alliance"
            data={mockKingdomData.killsByAlliance}
            type="bar"
          />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ChartCard
            title="Power Distribution"
            data={mockKingdomData.powerDistribution}
            type="pie"
          />
          <ChartCard
            title="Kills Distribution"
            data={mockKingdomData.killsDistribution}
            type="pie"
          />
        </div>
      </section>

      <footer className={`mt-12 text-center ${theme === 'light' ? 'text-gray-500' : 'text-gray-500'}`}>
        <p>&copy; {new Date().getFullYear()} Asahikawa 2358 Data. All rights reserved.</p>
      </footer>
    </div>
  );
};

export default AsahikawaDashboardPage;
