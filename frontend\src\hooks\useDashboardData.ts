import { useQuery } from '@tanstack/react-query';
import React, { useMemo, useCallback } from 'react';
import { getKvKList } from '../services/kvkService';
import { KvKData, ScanData, PlayerScanData } from '../types/dataTypes';
import { fetchScans } from '../services/scanService';
import { getDashboardData, getGeneralPerformanceSummary } from '../api/api';

export interface KvKScanData {
  latestScan?: ScanData;
  previousScan?: ScanData;
  baselineScan?: ScanData;
}

export const useDashboardData = () => {
  const { data: kvkList = [], isLoading: isLoadingKvKList, error: kvkListError } = useQuery<KvKData[], Error>({
    queryKey: ['kvkList'],
    queryFn: getKvKList,
  });

  const { data: scans = [], isLoading: isLoadingScans, error: scansError } = useQuery<ScanData[], Error>({
    queryKey: ['scansList'],
    queryFn: fetchScans,
  });

  // Get real dashboard data from API
  const { data: dashboardData, isLoading: isLoadingDashboard, error: dashboardError } = useQuery({
    queryKey: ['dashboardData'],
    queryFn: getDashboardData,
  });

  // Get real performance data from API
  const { data: performanceData, isLoading: isLoadingPerformance, error: performanceError } = useQuery({
    queryKey: ['generalPerformance'],
    queryFn: () => getGeneralPerformanceSummary(50),
  });

  const activeKvK = useMemo(() => {
    return kvkList.find(kvk => kvk.status === 'active') ||
           kvkList.find(kvk => kvk.status === 'upcoming') ||
           kvkList[0]; // Fallback to the first KvK if no active/upcoming
  }, [kvkList]);

  const getKvKScans = useCallback((kvkId: string | undefined): KvKScanData => {
    if (!kvkId || !scans || scans.length === 0) return {};
    const kvkScans = scans.filter(scan => scan.kvkId === kvkId);
    kvkScans.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    const latestScan = kvkScans[0];
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    const previousScan = kvkScans.find(scan => new Date(scan.date) <= oneDayAgo) || kvkScans[1];
    const baselineScan = kvkScans.find(scan => scan.isBaseline);

    return { latestScan, previousScan, baselineScan };
  }, [scans]);

  const recentCompletedKvKs = useMemo(() => {
    if (!kvkList || !activeKvK) return [];
    return kvkList
      .filter(kvk => kvk.id !== activeKvK?.id && kvk.status === 'completed')
      .sort((a, b) => new Date(b.endDate || 0).getTime() - new Date(a.endDate || 0).getTime());
  }, [kvkList, activeKvK]);

  // Calculate top performers from real performance data
  const topPerformers = useMemo(() => {
    if (!performanceData || !performanceData.performance_data) {
      return {
        topPowerPlayers: [],
        topKillsPlayers: [],
        topT45KillsPlayers: [],
        topDeadsPlayers: []
      };
    }

    const players = performanceData.performance_data.map((player: any) => ({
      governorId: player.governor_id || player.player_id?.toString() || '',
      name: player.player_name || 'Unknown',
      alliance: player.alliance || '',
      power: player.current_power || 0,
      killPoints: player.current_kp || 0,
      deads: player.current_dead || 0,
      t45Kills: (player.current_t4_kills || 0) + (player.current_t5_kills || 0), // T4 + T5 kills
      t1Kills: player.current_t1_kills || 0,
      t2Kills: player.current_t2_kills || 0,
      t3Kills: player.current_t3_kills || 0,
      t4Kills: player.current_t4_kills || 0,
      t5Kills: player.current_t5_kills || 0,
      ranged: 0,
      rssGathered: 0,
      rssAssistance: 0,
      helps: 0,
      // Performance gains
      powerGain: player.power_delta || 0,
      killPointsGain: player.kp_delta || 0,
      deadsGain: player.dead_delta || 0,
      t45KillsGain: (player.t4_delta || 0) + (player.t5_delta || 0)
    }));

    return {
      topPowerPlayers: [...players].sort((a, b) => b.power - a.power).slice(0, 10),
      topKillsPlayers: [...players].sort((a, b) => b.killPointsGain - a.killPointsGain).slice(0, 10), // Sort by gains, not totals
      topT45KillsPlayers: [...players].sort((a, b) => (b.t45KillsGain || 0) - (a.t45KillsGain || 0)).slice(0, 10),
      topDeadsPlayers: [...players].sort((a, b) => b.deadsGain - a.deadsGain).slice(0, 10) // Sort by gains, not totals
    };
  }, [performanceData]);

  const topPowerGainer = topPerformers.topPowerPlayers[0] || null;
  const topKillsGainer = topPerformers.topKillsPlayers[0] || null;
  const topT45KillsGainer = topPerformers.topT45KillsPlayers[0] || null;
  const topDeadsGainer = topPerformers.topDeadsPlayers[0] || null;

  return {
    kvkList,
    isLoadingKvKList,
    kvkListError,
    scans,
    isLoadingScans,
    scansError,
    activeKvK,
    getKvKScans,
    recentCompletedKvKs,
    topPowerGainer,
    topKillsGainer,
    topT45KillsGainer,
    topDeadsGainer,
    topPowerPlayers: topPerformers.topPowerPlayers,
    topKillsPlayers: topPerformers.topKillsPlayers,
    topDeadsPlayers: topPerformers.topDeadsPlayers,
    topT45KillsPlayers: topPerformers.topT45KillsPlayers,
    // Real dashboard data
    dashboardData,
    isLoadingDashboard,
    dashboardError,
    // Real performance data
    performanceData,
    isLoadingPerformance,
    performanceError,
    // Combined loading state
    isLoading: isLoadingKvKList || isLoadingScans || isLoadingDashboard || isLoadingPerformance,
  };
};
