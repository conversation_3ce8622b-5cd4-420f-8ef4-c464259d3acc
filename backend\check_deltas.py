#!/usr/bin/env python3

import requests
import time
from sqlalchemy.orm import Session
from database import SessionLocal
import models
import crud

def check_deltas():
    """Check if delta stats are being calculated and stored properly"""
    base_url = 'http://127.0.0.1:8000/api'

    print('🔍 Checking Delta Stats...\n')

    # Check database directly
    db = SessionLocal()
    try:
        # Get all scans
        scans = crud.get_scans(db)
        print(f'📊 Found {len(scans)} scans in database:')
        for scan in scans:
            print(f'   - {scan.name} (ID: {scan.id}) - {len(scan.player_stats)} players')

        # Check for delta stats
        delta_stats = db.query(models.DeltaStat).all()
        print(f'\n📈 Found {len(delta_stats)} delta stats in database')

        if delta_stats:
            for ds in delta_stats[:5]:  # Show first 5
                print(f'   - Player {ds.player_id}: KP Δ={ds.kill_points_delta}, Power Δ={ds.power_delta}')
        else:
            print('   ❌ No delta stats found!')

            # Check if we have baseline and latest scans
            baseline_scan = crud.get_baseline_scan(db)
            latest_scan = crud.get_latest_scan(db)

            if baseline_scan and latest_scan and baseline_scan.id != latest_scan.id:
                print(f'\n🔧 Found baseline scan {baseline_scan.name} and latest scan {latest_scan.name}')
                print('   Attempting to calculate delta stats...')

                # Import services to calculate deltas
                import services
                services.calculate_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)

                # Check again
                delta_stats = db.query(models.DeltaStat).all()
                print(f'   ✅ Created {len(delta_stats)} delta stats!')
            else:
                print('   ⚠️ Missing baseline or latest scan for delta calculation')

        # Test API endpoints
        print('\n🌐 Testing API endpoints...')

        # Test dashboard
        try:
            dashboard_response = requests.get(f'{base_url}/dashboard/')
            print(f'   Dashboard: {dashboard_response.status_code}')
            if dashboard_response.status_code == 200:
                data = dashboard_response.json()
                print(f'     Total KP: {data.get("total_kill_points", 0):,}')
                print(f'     KP Gain: {data.get("kill_points_gain", 0):,}')
        except Exception as e:
            print(f'   Dashboard error: {e}')

        # Test performance summary
        try:
            perf_response = requests.get(f'{base_url}/reports/kvk/performance_summary?kvk_id=1&limit=3')
            print(f'   Performance: {perf_response.status_code}')
            if perf_response.status_code == 200:
                data = perf_response.json()
                print(f'     Total players: {data["summary_stats"]["total_players"]}')
                print(f'     Performance data: {len(data["performance_data"])} players')
        except Exception as e:
            print(f'   Performance error: {e}')

    finally:
        db.close()

if __name__ == "__main__":
    check_deltas()
