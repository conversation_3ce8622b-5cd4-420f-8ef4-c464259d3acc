from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import crud_kvk, schemas, models
from database import get_db

router = APIRouter(
    prefix="/kvks",
    tags=["KvKs"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=schemas.KvK, status_code=status.HTTP_201_CREATED)
def create_kvk(
    kvk: schemas.KvKCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new KvK.
    - **name**: Unique name for the KvK
    - **start_date**: When the KvK starts
    - **end_date**: When the KvK ends (optional)
    - **status**: Status of the KvK (upcoming, active, completed)
    - **season**: Season number
    """
    try:
        return crud_kvk.create_kvk(db=db, kvk=kvk)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@router.get("/", response_model=List[schemas.KvK])
def read_kvks(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Retrieve a list of all KvKs, ordered by most recent first.
    """
    kvks = crud_kvk.get_kvks(db, skip=skip, limit=limit)
    return kvks

@router.get("/active", response_model=schemas.KvK)
def read_active_kvk(db: Session = Depends(get_db)):
    """
    Get the currently active KvK.
    """
    kvk = crud_kvk.get_active_kvk(db)
    if kvk is None:
        raise HTTPException(status_code=404, detail="No active KvK found")
    return kvk

@router.get("/{kvk_id}", response_model=schemas.KvK)
def read_kvk(
    kvk_id: int,
    db: Session = Depends(get_db)
):
    """
    Retrieve a specific KvK by its ID.
    """
    db_kvk = crud_kvk.get_kvk(db, kvk_id=kvk_id)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")
    return db_kvk

@router.put("/{kvk_id}", response_model=schemas.KvK)
def update_kvk(
    kvk_id: int,
    kvk_update: schemas.KvKUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an existing KvK.
    """
    db_kvk = crud_kvk.update_kvk(db, kvk_id=kvk_id, kvk_update=kvk_update)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")
    return db_kvk

@router.delete("/{kvk_id}")
def delete_kvk(
    kvk_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete a KvK.
    """
    db_kvk = crud_kvk.delete_kvk(db, kvk_id=kvk_id)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")
    return {"message": "KvK deleted successfully"}

@router.patch("/{kvk_id}/status")
def update_kvk_status(
    kvk_id: int,
    status: str,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db)
):
    """
    Update KvK status and optionally set end date.
    """
    if status not in ["upcoming", "active", "completed"]:
        raise HTTPException(status_code=400, detail="Invalid status. Must be 'upcoming', 'active', or 'completed'")

    db_kvk = crud_kvk.update_kvk_status(db, kvk_id=kvk_id, status=status, end_date=end_date)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")
    return db_kvk

@router.get("/{kvk_id}/scans", response_model=List[schemas.ScanWithPlayerCount])
def read_kvk_scans(
    kvk_id: int,
    db: Session = Depends(get_db)
):
    """
    Get all scans for a specific KvK.
    """
    # First check if KvK exists
    db_kvk = crud_kvk.get_kvk(db, kvk_id=kvk_id)
    if db_kvk is None:
        raise HTTPException(status_code=404, detail="KvK not found")

    scans = crud_kvk.get_kvk_scans(db, kvk_id=kvk_id)

    # Add player count to each scan
    scans_with_count = []
    for scan in scans:
        player_count = db.query(models.PlayerStat).filter(models.PlayerStat.scan_id == scan.id).count()
        scan_dict = {
            "id": scan.id,
            "name": scan.name,
            "is_baseline": scan.is_baseline,
            "kvk_id": scan.kvk_id,
            "kvk_phase": scan.kvk_phase,
            "timestamp": scan.timestamp,
            "player_count": player_count
        }
        scans_with_count.append(scan_dict)

    return scans_with_count