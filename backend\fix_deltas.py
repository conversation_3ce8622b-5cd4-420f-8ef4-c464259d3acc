#!/usr/bin/env python3

import requests
from database import SessionLocal
import models
import crud
import services

def fix_deltas():
    """Force calculate delta stats between baseline and latest scan"""
    print('🔧 Fixing Delta Stats...\n')
    
    # Check database directly
    db = SessionLocal()
    try:
        # Get all scans
        scans = crud.get_scans(db)
        print(f'📊 Found {len(scans)} scans in database:')
        for scan in scans:
            print(f'   - {scan.name} (ID: {scan.id}) - Baseline: {scan.is_baseline} - Players: {len(scan.player_stats)}')
        
        # Get baseline and latest scans
        baseline_scan = crud.get_baseline_scan(db)
        latest_scan = crud.get_latest_scan(db)
        
        if not baseline_scan:
            print('\n❌ No baseline scan found!')
            return
            
        if not latest_scan:
            print('\n❌ No latest scan found!')
            return
            
        print(f'\n📋 Baseline scan: {baseline_scan.name} (ID: {baseline_scan.id})')
        print(f'📋 Latest scan: {latest_scan.name} (ID: {latest_scan.id})')
        
        if baseline_scan.id == latest_scan.id:
            print('\n⚠️ Baseline and latest scan are the same - no deltas to calculate')
            return
        
        # Check existing delta stats
        existing_deltas = crud.get_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)
        print(f'\n📈 Existing delta stats: {len(existing_deltas)}')
        
        # Force calculate delta stats
        print('\n🔧 Calculating delta stats...')
        services.calculate_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)
        
        # Check again
        new_deltas = crud.get_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)
        print(f'✅ Delta stats after calculation: {len(new_deltas)}')
        
        if new_deltas:
            print('\n📊 Sample delta stats:')
            for i, ds in enumerate(new_deltas[:5]):
                player_name = ds.player.name if ds.player else f'Player {ds.player_id}'
                print(f'   {i+1}. {player_name}: KP Δ={ds.kill_points_delta:,}, Power Δ={ds.power_delta:,}')
        
        # Test API endpoints
        print('\n🌐 Testing API endpoints...')
        
        # Test dashboard
        try:
            dashboard_response = requests.get('http://localhost:8000/api/dashboard/')
            print(f'   Dashboard: {dashboard_response.status_code}')
            if dashboard_response.status_code == 200:
                data = dashboard_response.json()
                print(f'     Total KP: {data.get("total_kill_points", 0):,}')
                print(f'     KP Gain: {data.get("kill_points_gain", 0):,}')
                print(f'     Total Power: {data.get("total_power", 0):,}')
            else:
                print(f'     Error: {dashboard_response.text[:100]}')
        except Exception as e:
            print(f'   Dashboard error: {e}')
        
        # Test performance summary
        try:
            perf_response = requests.get('http://localhost:8000/api/reports/kvk/performance_summary?kvk_id=1&limit=5')
            print(f'   Performance: {perf_response.status_code}')
            if perf_response.status_code == 200:
                data = perf_response.json()
                print(f'     Total players: {data["summary_stats"]["total_players"]}')
                print(f'     Performance data: {len(data["performance_data"])} players')
                if data["performance_data"]:
                    top_player = data["performance_data"][0]
                    print(f'     Top performer: {top_player["player_name"]} - KP: {top_player["kill_points_gain"]:,}')
            else:
                print(f'     Error: {perf_response.text[:100]}')
        except Exception as e:
            print(f'   Performance error: {e}')
            
    finally:
        db.close()

if __name__ == "__main__":
    fix_deltas()
