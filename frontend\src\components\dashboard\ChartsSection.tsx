import React, { memo } from 'react'; // Import memo
import ChartCard from '../ChartCard'; // Assuming ChartCard is in components directory
import { useTheme } from '../../contexts/ThemeContext';
import { KingdomData } from '../../types/dataTypes'; // Assuming this type exists

interface ChartsSectionProps {
  kingdomData: KingdomData; // Or a more specific type for chart data
}

const ChartsSection: React.FC<ChartsSectionProps> = ({ kingdomData }) => {
  const { theme } = useTheme();
  return (
    <section className="mb-10">
      <h2 className={`text-2xl font-semibold mb-6 ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
        Kingdom Statistics
      </h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <ChartCard
          title="Power by Alliance"
          data={kingdomData.powerByAlliance}
          type="bar"
        />
        <ChartCard
          title="Kills by Alliance"
          data={kingdomData.killsByAlliance}
          type="bar"
        />
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard
          title="Power Distribution"
          data={kingdomData.powerDistribution}
          type="pie"
        />
        <ChartCard
          title="Kills Distribution"
          data={kingdomData.killsDistribution}
          type="pie"
        />
      </div>
    </section>
  );
};

export default memo(ChartsSection); // Wrap with memo
