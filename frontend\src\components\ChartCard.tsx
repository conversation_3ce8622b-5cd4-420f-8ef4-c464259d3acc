import React, { memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import {
  BarChart, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer,
  <PERSON><PERSON><PERSON>, <PERSON>, Cell
} from 'recharts';

interface ChartData {
  name: string;
  value: number;
}

interface ChartCardProps {
  title: string;
  data: ChartData[];
  type: 'bar' | 'pie';
  dataKey?: string;
  colors?: string[];
}

const ChartCard: React.FC<ChartCardProps> = ({
  title,
  data,
  type = 'bar',
  dataKey = 'value',
  colors = ['#3b82f6', '#10b981', '#f59e0b', '#ec4899', '#8b5cf6', '#ef4444', '#06b6d4', '#84cc16']
}) => {
  const { theme } = useTheme();

  const renderChart = () => {
    if (type === 'bar') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <Bar<PERSON>hart
            data={data}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke={theme === 'light' ? '#e5e7eb' : '#374151'} />
            <XAxis
              dataKey="name"
              tick={{ fill: theme === 'light' ? '#4b5563' : '#9ca3af' }}
            />
            <YAxis
              tick={{ fill: theme === 'light' ? '#4b5563' : '#9ca3af' }}
              tickFormatter={(value) => {
                if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
                if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
                return value.toString();
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: theme === 'light' ? '#ffffff' : '#1f2937',
                borderColor: theme === 'light' ? '#e5e7eb' : '#374151',
                color: theme === 'light' ? '#111827' : '#f9fafb',
              }}
              formatter={(value: number) => {
                if (value >= 1000000) return [`${(value / 1000000).toFixed(2)}M`, ''];
                if (value >= 1000) return [`${(value / 1000).toFixed(1)}K`, ''];
                return [value, ''];
              }}
            />
            <Legend wrapperStyle={{ color: theme === 'light' ? '#4b5563' : '#9ca3af' }} />
            <Bar
              dataKey={dataKey}
              fill="url(#colorGradient)"
              radius={[4, 4, 0, 0]}
            >
              <defs>
                <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#1d4ed8" stopOpacity={0.6}/>
                </linearGradient>
              </defs>
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      );
    } else if (type === 'pie') {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={100}
              fill="#8884d8"
              dataKey={dataKey}
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{
                backgroundColor: theme === 'light' ? '#ffffff' : '#1f2937',
                borderColor: theme === 'light' ? '#e5e7eb' : '#374151',
                color: theme === 'light' ? '#111827' : '#f9fafb',
              }}
              formatter={(value: number) => {
                if (value >= 1000000) return [`${(value / 1000000).toFixed(2)}M`, ''];
                if (value >= 1000) return [`${(value / 1000).toFixed(1)}K`, ''];
                return [value, ''];
              }}
            />
            <Legend wrapperStyle={{ color: theme === 'light' ? '#4b5563' : '#9ca3af' }} />
          </PieChart>
        </ResponsiveContainer>
      );
    }

    return null;
  };

  return (
    <div className={`p-6 rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl ${
      theme === 'light'
        ? 'bg-white hover:bg-gray-50'
        : 'bg-gray-800 hover:bg-gray-750'
    }`}>
      <div className="flex items-center mb-6">
        <div className={`w-1 h-8 rounded-full mr-4 ${
          theme === 'light' ? 'bg-gradient-to-b from-blue-500 to-indigo-600' : 'bg-gradient-to-b from-blue-400 to-indigo-500'
        }`}></div>
        <h3 className={`text-xl font-bold ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
          {title}
        </h3>
      </div>
      <div className="relative">
        {renderChart()}
      </div>
    </div>
  );
};

export default memo(ChartCard);
