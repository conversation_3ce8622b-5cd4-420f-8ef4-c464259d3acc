import { ScanData, PlayerScanData, KvKPhase } from '../types/dataTypes';
import axios from '../config/axios';

/**
 * Convert backend scan data to frontend format
 */
const convertBackendScanToFrontend = (backendScan: any): ScanData => {
  return {
    id: backendScan.id.toString(),
    name: backendScan.name,
    date: backendScan.timestamp,
    isBaseline: backendScan.is_baseline || false,
    kvkId: backendScan.kvk_id ? backendScan.kvk_id.toString() : null,
    kvkPhase: backendScan.kvk_phase || KvKPhase.KingdomVsKingdom,
    players: [] // Players will be loaded separately
  };
};

/**
 * Convert backend scan data with player count to frontend format
 */
const convertBackendScanWithCountToFrontend = (backendScan: any): ScanData => {
  // Create mock players array with the correct length for display purposes
  const mockPlayers = Array(backendScan.player_count || 0).fill(null).map((_, index) => ({
    governorId: `mock-${index}`,
    name: `Player ${index + 1}`,
    alliance: '',
    power: 0,
    killPoints: 0,
    deads: 0,
    t1Kills: 0,
    t2Kills: 0,
    t3Kills: 0,
    t4Kills: 0,
    t5Kills: 0,
    t45Kills: 0,
    ranged: 0,
    rssGathered: 0,
    rssAssistance: 0,
    helps: 0
  }));

  return {
    id: backendScan.id.toString(),
    name: backendScan.name,
    date: backendScan.timestamp,
    isBaseline: backendScan.is_baseline || false,
    kvkId: backendScan.kvk_id ? backendScan.kvk_id.toString() : null,
    kvkPhase: backendScan.kvk_phase || KvKPhase.KingdomVsKingdom,
    players: mockPlayers // Use mock players array with correct count
  };
};

/**
 * Convert backend player stat to frontend format
 */
const convertBackendPlayerToFrontend = (stat: any): PlayerScanData => {
  return {
    governorId: stat.player.governor_id || stat.player.id.toString(),
    name: stat.player.name,
    alliance: stat.player.alliance,
    power: stat.power,
    killPoints: stat.total_kill_points,
    deads: stat.dead_troops,
    t1Kills: stat.kill_points_t1 || 0,
    t2Kills: stat.kill_points_t2 || 0,
    t3Kills: stat.kill_points_t3 || 0,
    t4Kills: stat.kill_points_t4 || 0,
    t5Kills: stat.kill_points_t5 || 0,
    totalKills: stat.total_kill_points,
    t45Kills: stat.t45_kills || 0,
    ranged: stat.ranged || 0,
    rssGathered: stat.rss_gathered || 0,
    rssAssisted: stat.rss_assistance || 0,
    helps: stat.helps || 0,
    statId: stat.id,
    scanId: stat.scan_id,
    timestamp: stat.timestamp
  };
};

/**
 * Fetch all scans from the backend API
 * @returns Promise<ScanData[]> Array of scan data
 */
export const fetchScans = async (): Promise<ScanData[]> => {
  try {
    const response = await axios.get('/api/scans/');
    return response.data.map(convertBackendScanWithCountToFrontend);
  } catch (error) {
    console.error('Error fetching scans:', error);
    throw error;
  }
};

/**
 * Fetch a specific scan by ID with player data
 * @param scanId - The ID of the scan to fetch
 * @returns Promise<ScanData | null> The scan data or null if not found
 */
export const fetchScanById = async (scanId: string): Promise<ScanData | null> => {
  try {
    // Fetch scan details
    const scanResponse = await axios.get(`/api/scans/${scanId}`);
    const scan = convertBackendScanToFrontend(scanResponse.data);

    // Fetch player stats for this scan
    const statsResponse = await axios.get(`/api/scans/${scanId}/stats`);
    scan.players = statsResponse.data.map(convertBackendPlayerToFrontend);

    return scan;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null;
    }
    console.error('Error fetching scan by ID:', error);
    throw error;
  }
};

/**
 * Fetch scans for a specific KvK
 * @param kvkId - The ID of the KvK to fetch scans for
 * @returns Promise<ScanData[]> Array of scan data for the specified KvK
 */
export const fetchScansByKvK = async (kvkId: string): Promise<ScanData[]> => {
  try {
    const response = await axios.get(`/api/kvks/${kvkId}/scans`);
    return response.data.map((scan: any) => convertBackendScanWithCountToFrontend(scan));
  } catch (error) {
    console.error('Error fetching scans by KvK:', error);
    throw error;
  }
};

/**
 * Get the baseline scan for a specific KvK
 * @param kvkId - The ID of the KvK to get baseline scan for
 * @returns Promise<ScanData | null> The baseline scan or null if not found
 */
export const getBaselineScan = async (kvkId: string): Promise<ScanData | null> => {
  try {
    const scans = await fetchScans();
    const baselineScan = scans.find(scan => scan.isBaseline);
    if (baselineScan) {
      // Fetch full scan data with players
      return fetchScanById(baselineScan.id);
    }
    return null;
  } catch (error) {
    console.error('Error fetching baseline scan:', error);
    throw error;
  }
};

/**
 * Set a scan as baseline
 * @param scanId - The ID of the scan to set as baseline
 * @returns Promise<ScanData> The updated scan
 */
export const setBaselineScan = async (scanId: string): Promise<ScanData> => {
  try {
    const response = await axios.post(`/api/scans/set_baseline/${scanId}`);
    return convertBackendScanToFrontend(response.data);
  } catch (error) {
    console.error('Error setting baseline scan:', error);
    throw error;
  }
};