#!/usr/bin/env python3

import requests
import time

def quick_test():
    print('Quick test...')

    # Create KvK
    kvk_data = {'name': f'Test KvK {int(time.time())}', 'start_date': '2024-01-01T00:00:00', 'season': 1, 'status': 'upcoming'}
    kvk_response = requests.post('http://127.0.0.1:8000/api/kvks/', json=kvk_data)
    print(f'KvK: {kvk_response.status_code}')

    if kvk_response.status_code == 201:
        kvk = kvk_response.json()
        print(f'Created KvK {kvk["id"]} - {kvk["status"]}')
        
        # Upload scan
        with open('../TOP300-2025-04-25-TOP300-25-04-2025-[5q3onfa5].xlsx', 'rb') as f:
            files = {'file': f}
            url = f'http://127.0.0.1:8000/api/scans/upload?scan_name=Test&is_baseline=true&kvk_id={kvk["id"]}'
            scan_response = requests.post(url, files=files)
            print(f'Scan: {scan_response.status_code}')
            
            if scan_response.status_code == 200:
                scan = scan_response.json()
                print(f'Uploaded scan {scan["id"]} to KvK {scan.get("kvk_id")}')
                
                # Test performance
                perf_response = requests.get(f'http://127.0.0.1:8000/api/reports/kvk/performance_summary?kvk_id={kvk["id"]}&limit=2')
                print(f'Performance: {perf_response.status_code}')
                
                if perf_response.status_code == 200:
                    data = perf_response.json()
                    print('SUCCESS!')
                    print(f'Total players: {data["summary_stats"]["total_players"]}')
                    print(f'Baseline scan: {data["scan_period"]["baseline_scan_name"]}')
                    print(f'Latest scan: {data["scan_period"]["latest_scan_name"]}')
                    print(f'Same scan: {data["scan_period"]["baseline_scan_id"] == data["scan_period"]["latest_scan_id"]}')
                else:
                    print(f'Error: {perf_response.text}')
            else:
                print(f'Scan failed: {scan_response.text}')
    else:
        print(f'KvK failed: {kvk_response.text}')

if __name__ == "__main__":
    quick_test()
