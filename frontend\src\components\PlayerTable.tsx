import React, { useState, useMemo, memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { PlayerScanData } from '../types/dataTypes';
import { formatNumber } from '../utils/formatters';
import {
  FaSearch,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaUsers,
  FaShieldAlt,
  FaCrosshairs,
  FaSkullCrossbones,
  FaTrophy
} from 'react-icons/fa';

interface PlayerTableProps {
  players: PlayerScanData[];
  title?: string;
  showAlliance?: boolean;
  pageSize?: number;
}

const PlayerTable: React.FC<PlayerTableProps> = ({
  players,
  title = 'Players',
  showAlliance = true,
  pageSize = 25
}) => {
  const { theme } = useTheme();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof PlayerScanData>('power');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Filter players based on search term
  const filteredPlayers = useMemo(() => {
    if (!players || players.length === 0) return [];

    return players.filter(player => {
      const searchLower = searchTerm.toLowerCase();
      return (
        (player.name && player.name.toLowerCase().includes(searchLower)) ||
        (player.governorId && player.governorId.toLowerCase().includes(searchLower)) ||
        (player.alliance && player.alliance.toLowerCase().includes(searchLower))
      );
    });
  }, [players, searchTerm]);

  // Sort players
  const sortedPlayers = useMemo(() => {
    return [...filteredPlayers].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === undefined || bValue === undefined) return 0;

      // Handle string comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // Handle number comparison
      return sortDirection === 'asc'
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number);
    });
  }, [filteredPlayers, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(sortedPlayers.length / pageSize);
  const paginatedPlayers = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedPlayers.slice(startIndex, startIndex + pageSize);
  }, [sortedPlayers, currentPage, pageSize]);

  // Handle sort
  const handleSort = (field: keyof PlayerScanData) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc'); // Default to descending for new sort field
    }
  };

  return (
    <div className={`rounded-2xl overflow-hidden shadow-xl transition-all duration-300 ${
      theme === 'light' ? 'bg-white' : 'bg-gray-800'
    }`}>
      {/* Enhanced Header */}
      <div className={`px-6 py-6 ${
        theme === 'light'
          ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100'
          : 'bg-gradient-to-r from-blue-900/30 to-indigo-900/30 border-b border-gray-700'
      }`}>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center">
            <div className={`p-2 rounded-xl mr-3 ${
              theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
            }`}>
              <FaUsers className="text-lg" />
            </div>
            <div>
              <h3 className={`text-xl font-bold ${
                theme === 'light' ? 'text-gray-900' : 'text-white'
              }`}>
                {title}
              </h3>
              <p className={`text-sm ${
                theme === 'light' ? 'text-gray-600' : 'text-gray-400'
              }`}>
                {filteredPlayers.length} player{filteredPlayers.length !== 1 ? 's' : ''} found
              </p>
            </div>
          </div>

          {/* Enhanced Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className={`text-sm ${
                theme === 'light' ? 'text-gray-400' : 'text-gray-500'
              }`} />
            </div>
            <input
              type="text"
              placeholder="Search players, IDs, alliances..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
              className={`pl-10 pr-4 py-2 rounded-xl border transition-all duration-300 ${
                theme === 'light'
                  ? 'border-gray-300 bg-white text-gray-700 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                  : 'border-gray-600 bg-gray-700 text-white placeholder-gray-400 focus:border-blue-400 focus:ring-2 focus:ring-blue-800'
              } focus:outline-none`}
            />
          </div>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead className={`${
            theme === 'light' ? 'bg-gradient-to-r from-gray-50 to-gray-100' : 'bg-gradient-to-r from-gray-900/50 to-gray-800/50'
          }`}>
            <tr>
              <th
                scope="col"
                className={`px-6 py-4 text-left text-xs font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                } uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200`}
                onClick={() => handleSort('governorId')}
              >
                <div className="flex items-center space-x-2">
                  <span>ID</span>
                  {sortField === 'governorId' ? (
                    sortDirection === 'asc' ? <FaSortUp className="text-blue-500" /> : <FaSortDown className="text-blue-500" />
                  ) : (
                    <FaSort className="text-gray-400" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className={`px-6 py-4 text-left text-xs font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                } uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200`}
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center space-x-2">
                  <span>Name</span>
                  {sortField === 'name' ? (
                    sortDirection === 'asc' ? <FaSortUp className="text-blue-500" /> : <FaSortDown className="text-blue-500" />
                  ) : (
                    <FaSort className="text-gray-400" />
                  )}
                </div>
              </th>
              {showAlliance && (
                <th
                  scope="col"
                  className={`px-6 py-4 text-left text-xs font-semibold ${
                    theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                  } uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200`}
                  onClick={() => handleSort('alliance')}
                >
                  <div className="flex items-center space-x-2">
                    <FaShieldAlt className="text-purple-500" />
                    <span>Alliance</span>
                    {sortField === 'alliance' ? (
                      sortDirection === 'asc' ? <FaSortUp className="text-blue-500" /> : <FaSortDown className="text-blue-500" />
                    ) : (
                      <FaSort className="text-gray-400" />
                    )}
                  </div>
                </th>
              )}
              <th
                scope="col"
                className={`px-6 py-4 text-right text-xs font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                } uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200`}
                onClick={() => handleSort('power')}
              >
                <div className="flex items-center justify-end space-x-2">
                  <FaShieldAlt className="text-green-500" />
                  <span>Power</span>
                  {sortField === 'power' ? (
                    sortDirection === 'asc' ? <FaSortUp className="text-blue-500" /> : <FaSortDown className="text-blue-500" />
                  ) : (
                    <FaSort className="text-gray-400" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className={`px-6 py-4 text-right text-xs font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                } uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200`}
                onClick={() => handleSort('killPoints')}
              >
                <div className="flex items-center justify-end space-x-2">
                  <FaCrosshairs className="text-red-500" />
                  <span>Kill Points</span>
                  {sortField === 'killPoints' ? (
                    sortDirection === 'asc' ? <FaSortUp className="text-blue-500" /> : <FaSortDown className="text-blue-500" />
                  ) : (
                    <FaSort className="text-gray-400" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className={`px-6 py-4 text-right text-xs font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                } uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200`}
                onClick={() => handleSort('deads')}
              >
                <div className="flex items-center justify-end space-x-2">
                  <FaSkullCrossbones className="text-yellow-500" />
                  <span>Deads</span>
                  {sortField === 'deads' ? (
                    sortDirection === 'asc' ? <FaSortUp className="text-blue-500" /> : <FaSortDown className="text-blue-500" />
                  ) : (
                    <FaSort className="text-gray-400" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className={`px-6 py-4 text-right text-xs font-semibold ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                } uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200`}
                onClick={() => handleSort('t45Kills')}
              >
                <div className="flex items-center justify-end space-x-2">
                  <FaTrophy className="text-purple-500" />
                  <span>T4-5 Kills</span>
                  {sortField === 't45Kills' ? (
                    sortDirection === 'asc' ? <FaSortUp className="text-blue-500" /> : <FaSortDown className="text-blue-500" />
                  ) : (
                    <FaSort className="text-gray-400" />
                  )}
                </div>
              </th>
            </tr>
          </thead>
          <tbody className={`${theme === 'light' ? 'bg-white' : 'bg-gray-800'} divide-y ${theme === 'light' ? 'divide-gray-100' : 'divide-gray-700'}`}>
            {paginatedPlayers.map((player, index) => (
              <tr
                key={player.governorId}
                className={`transition-all duration-200 ${
                  theme === 'light'
                    ? 'hover:bg-blue-50 hover:shadow-sm'
                    : 'hover:bg-gray-700/50 hover:shadow-sm'
                }`}
              >
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-mono ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                }`}>
                  {player.governorId}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-semibold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold mr-3 ${
                      index < 3
                        ? 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white'
                        : theme === 'light'
                        ? 'bg-gray-100 text-gray-600'
                        : 'bg-gray-700 text-gray-300'
                    }`}>
                      {((currentPage - 1) * pageSize) + index + 1}
                    </div>
                    {player.name}
                  </div>
                </td>
                {showAlliance && (
                  <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  }`}>
                    {player.alliance ? (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        theme === 'light'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-purple-900/30 text-purple-300'
                      }`}>
                        {player.alliance}
                      </span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                )}
                <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-semibold ${
                  theme === 'light' ? 'text-green-700' : 'text-green-400'
                }`}>
                  {formatNumber(player.power)}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-semibold ${
                  theme === 'light' ? 'text-red-700' : 'text-red-400'
                }`}>
                  {formatNumber(player.killPoints)}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-semibold ${
                  theme === 'light' ? 'text-yellow-700' : 'text-yellow-400'
                }`}>
                  {formatNumber(player.deads)}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-semibold ${
                  theme === 'light' ? 'text-purple-700' : 'text-purple-400'
                }`}>
                  {formatNumber(player.t45Kills)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className={`px-4 py-3 flex items-center justify-between border-t ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                currentPage === 1
                  ? `${theme === 'light' ? 'bg-gray-100 text-gray-400' : 'bg-gray-700 text-gray-500'}`
                  : `${theme === 'light' ? 'bg-white text-gray-700 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={`ml-3 relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                currentPage === totalPages
                  ? `${theme === 'light' ? 'bg-gray-100 text-gray-400' : 'bg-gray-700 text-gray-500'}`
                  : `${theme === 'light' ? 'bg-white text-gray-700 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`
              }`}
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className={`text-sm ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                Showing <span className="font-medium">{(currentPage - 1) * pageSize + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, filteredPlayers.length)}
                </span>{' '}
                of <span className="font-medium">{filteredPlayers.length}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border text-sm font-medium ${
                    currentPage === 1
                      ? `${theme === 'light' ? 'bg-gray-100 text-gray-400 border-gray-300' : 'bg-gray-700 text-gray-500 border-gray-600'}`
                      : `${theme === 'light' ? 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'}`
                  }`}
                >
                  <span className="sr-only">First</span>
                  &laquo;
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 border text-sm font-medium ${
                    currentPage === 1
                      ? `${theme === 'light' ? 'bg-gray-100 text-gray-400 border-gray-300' : 'bg-gray-700 text-gray-500 border-gray-600'}`
                      : `${theme === 'light' ? 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'}`
                  }`}
                >
                  <span className="sr-only">Previous</span>
                  &lsaquo;
                </button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === pageNum
                          ? `${theme === 'light' ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'z-10 bg-blue-900 border-blue-500 text-blue-300'}`
                          : `${theme === 'light' ? 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'}`
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 border text-sm font-medium ${
                    currentPage === totalPages
                      ? `${theme === 'light' ? 'bg-gray-100 text-gray-400 border-gray-300' : 'bg-gray-700 text-gray-500 border-gray-600'}`
                      : `${theme === 'light' ? 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'}`
                  }`}
                >
                  <span className="sr-only">Next</span>
                  &rsaquo;
                </button>
                <button
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border text-sm font-medium ${
                    currentPage === totalPages
                      ? `${theme === 'light' ? 'bg-gray-100 text-gray-400 border-gray-300' : 'bg-gray-700 text-gray-500 border-gray-600'}`
                      : `${theme === 'light' ? 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'}`
                  }`}
                >
                  <span className="sr-only">Last</span>
                  &raquo;
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(PlayerTable);
