#!/usr/bin/env python3

import requests

def check_data():
    print('=== Checking Current Data State ===')
    
    # Check KvKs
    print('\n1. KvKs:')
    kvks_response = requests.get('http://127.0.0.1:8000/api/kvks/')
    if kvks_response.status_code == 200:
        kvks = kvks_response.json()
        print(f'   Found {len(kvks)} KvKs:')
        for kvk in kvks:
            print(f'   - KvK {kvk["id"]}: {kvk["name"]} (Status: {kvk["status"]})')
    else:
        print(f'   Error: {kvks_response.status_code}')
    
    # Check all scans
    print('\n2. All Scans:')
    scans_response = requests.get('http://127.0.0.1:8000/api/scans/')
    if scans_response.status_code == 200:
        scans = scans_response.json()
        print(f'   Found {len(scans)} scans:')
        for scan in scans:
            print(f'   - Scan {scan["id"]}: {scan["name"]}')
            print(f'     KvK ID: {scan.get("kvk_id", "None")}')
            print(f'     Baseline: {scan.get("is_baseline", False)}')
            print(f'     Timestamp: {scan.get("timestamp", "N/A")}')
            print()
    else:
        print(f'   Error: {scans_response.status_code}')
    
    # Check scans for KvK 1
    print('\n3. Scans for KvK 1:')
    try:
        kvk_scans_response = requests.get('http://127.0.0.1:8000/api/kvks/1/scans')
        print(f'   Status: {kvk_scans_response.status_code}')
        if kvk_scans_response.status_code == 200:
            kvk_scans = kvk_scans_response.json()
            print(f'   Found {len(kvk_scans)} scans for KvK 1:')
            for scan in kvk_scans:
                print(f'   - {scan["name"]} (ID: {scan["id"]}, Baseline: {scan.get("is_baseline", False)})')
        else:
            print(f'   Error: {kvk_scans_response.text}')
    except Exception as e:
        print(f'   Exception: {e}')
    
    # Test performance summary
    print('\n4. Performance Summary Test:')
    try:
        perf_response = requests.get('http://127.0.0.1:8000/api/reports/kvk/performance_summary?kvk_id=1&limit=3')
        print(f'   Status: {perf_response.status_code}')
        if perf_response.status_code == 200:
            data = perf_response.json()
            print('   ✅ SUCCESS!')
            print(f'   Total players: {data["summary_stats"]["total_players"]}')
        else:
            print(f'   ❌ Error: {perf_response.text}')
    except Exception as e:
        print(f'   Exception: {e}')

if __name__ == "__main__":
    check_data()
