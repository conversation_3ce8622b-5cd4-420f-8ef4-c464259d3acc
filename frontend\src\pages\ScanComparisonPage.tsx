import React, { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import ChartCard from '../components/ChartCard';
import { mockScans, getMockPerformanceMetrics } from '../services/mockRokData';
import { formatNumber } from '../services/performanceService';
import { FaExchangeAlt, FaChartBar, FaUsers, FaArrowUp, FaSearch, FaExclamationTriangle, FaSpinner, FaRegSadCry, FaRegSmileBeam } from 'react-icons/fa'; // Added icons

// Scan options from our mock data
console.log('ScanComparisonPage - mockScans:', mockScans);
const scanOptions = mockScans && mockScans.length > 0
  ? mockScans.map(scan => ({
      id: scan.id,
      name: scan.name,
      date: scan.date,
      phase: scan.kvkPhase
    }))
  : [];

const ScanComparisonPage: React.FC = () => {
  const { theme } = useTheme();
  const [baseScanId, setBaseScanId] = useState<string>(mockScans && mockScans.length > 0 ? mockScans[0].id : '');
  const [comparisonScanId, setComparisonScanId] = useState<string>(mockScans && mockScans.length > 2 ? mockScans[2].id : '');

  // Fetch data for the selected scans
  const { data: performanceData, isLoading, error } = useQuery(
    ['scanComparison', baseScanId, comparisonScanId],
    () => {
      console.log('Fetching comparison data with:', { baseScanId, comparisonScanId });
      if (!baseScanId || !comparisonScanId) {
        console.error('Invalid scan IDs:', { baseScanId, comparisonScanId });
        return null;
      }
      return getMockPerformanceMetrics(baseScanId, comparisonScanId);
    },
    {
      enabled: !!baseScanId && !!comparisonScanId && mockScans && mockScans.length > 0
    }
  );

  console.log('ScanComparisonPage - Query results:', { performanceData, isLoading, error });

  return (
    <div className={`p-4 sm:p-6 md:px-8 transition-colors duration-300 ${theme === 'light' ? 'bg-slate-100 text-slate-900' : 'bg-gray-900 text-gray-100'}`}>
      <header className="mb-8 p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}">
        <div className="flex items-center mb-2">
          <FaExchangeAlt className={`h-8 w-8 mr-3 ${theme === 'light' ? 'text-indigo-600' : 'text-indigo-400'}`} />
          <h1 className={`text-3xl font-bold ${theme === 'light' ? 'text-indigo-700' : 'text-indigo-400'}`}>
            Scan Comparison
          </h1>
        </div>
        <p className={`${theme === 'light' ? 'text-gray-600' : 'text-gray-400'} text-sm`}>
          Analyze player and alliance growth by comparing two different data scans.
        </p>
      </header>

      {/* Scan Selection - Redesigned */}
      <div className={`mb-8 p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-end">
          <div>
            <label htmlFor="baseScan" className={`block text-sm font-medium mb-2 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
              <FaCalendarAlt className="inline h-4 w-4 mr-1 mb-0.5" /> Base Scan (Earlier)
            </label>
            <select
              id="baseScan"
              value={baseScanId}
              onChange={(e) => setBaseScanId(e.target.value)}
              className={`block w-full px-3 py-2.5 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${theme === 'light'
                  ? 'bg-white border-gray-300 text-gray-900 placeholder-gray-400'
                  : 'bg-gray-700 border-gray-600 text-white placeholder-gray-500'}`}
            >
              {scanOptions.map(scan => (
                <option key={scan.id} value={scan.id}>
                  {scan.name} ({scan.date}) - {scan.phase}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="comparisonScan" className={`block text-sm font-medium mb-2 ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
              <FaCalendarAlt className="inline h-4 w-4 mr-1 mb-0.5" /> Comparison Scan (Later)
            </label>
            <select
              id="comparisonScan"
              value={comparisonScanId}
              onChange={(e) => setComparisonScanId(e.target.value)}
              className={`block w-full px-3 py-2.5 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${theme === 'light'
                  ? 'bg-white border-gray-300 text-gray-900 placeholder-gray-400'
                  : 'bg-gray-700 border-gray-600 text-white placeholder-gray-500'}`}
            >
              {scanOptions.map(scan => (
                <option key={scan.id} value={scan.id}>
                  {scan.name} ({scan.date}) - {scan.phase}
                </option>
              ))}
            </select>
          </div>
        </div>
        {(!baseScanId || !comparisonScanId) && (
          <p className={`mt-4 text-xs ${theme === 'light' ? 'text-yellow-700' : 'text-yellow-400'} flex items-center`}>
            <FaExclamationTriangle className="inline h-4 w-4 mr-1.5" /> Please select both a base and a comparison scan to see data.
          </p>
        )}
      </div>

      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FaSpinner className={`animate-spin h-12 w-12 mb-4 ${theme === 'light' ? 'text-indigo-600' : 'text-indigo-400'}`} />
          <p className={`text-lg font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>Loading Comparison Data...</p>
          <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Please wait while we fetch the numbers.</p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FaRegSadCry className={`h-16 w-16 mb-4 ${theme === 'light' ? 'text-red-500' : 'text-red-400'}`} />
          <div className={`p-4 rounded-lg ${theme === 'light' ? 'bg-red-50 text-red-700' : 'bg-red-900 text-red-300'} max-w-lg`}>
            <h3 className="font-bold text-lg mb-2">Error Loading Data</h3>
            <p className="text-sm">There was a problem loading the comparison data. Please try again or select different scans.</p>
            <p className="mt-2 text-xs">{error instanceof Error ? error.message : 'Unknown error'}</p>
          </div>
        </div>
      ) : !mockScans || mockScans.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FaSearch className={`h-16 w-16 mb-4 ${theme === 'light' ? 'text-yellow-500' : 'text-yellow-400'}`} />
          <div className={`p-4 rounded-lg ${theme === 'light' ? 'bg-yellow-50 text-yellow-700' : 'bg-yellow-900 text-yellow-300'} max-w-lg`}>
            <h3 className="font-bold text-lg mb-2">No Scan Data Available</h3>
            <p className="text-sm">There are no scans available for comparison. Please upload scan data first.</p>
          </div>
        </div>
      ) : performanceData ? (
        <>
          {/* Growth Charts - Redesigned */}
          <section className="mb-8">
            <div className="flex items-center mb-4">
              <FaChartBar className={`h-6 w-6 mr-3 ${theme === 'light' ? 'text-indigo-600' : 'text-indigo-400'}`} />
              <h2 className={`text-2xl font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
                Alliance Growth Snapshot
              </h2>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              <ChartCard
                title="Power Growth"
                data={performanceData.alliancePerformance.map(alliance => ({
                  name: alliance.name,
                  value: alliance.totalPower
                }))}
                type="bar"
                icon={<FaShieldAlt className={`mr-2 ${theme === 'light' ? 'text-blue-500' : 'text-blue-400' }`} />}
                theme={theme}
              />
              <ChartCard
                title="Kills Growth"
                data={performanceData.alliancePerformance.map(alliance => ({
                  name: alliance.name,
                  value: alliance.totalKillPoints
                }))}
                type="bar"
                icon={<FaFistRaised className={`mr-2 ${theme === 'light' ? 'text-red-500' : 'text-red-400' }`} />}
                theme={theme}
              />
              <ChartCard
                title="Dead Troops Growth"
                data={performanceData.alliancePerformance.map(alliance => ({
                  name: alliance.name,
                  value: alliance.totalDeadTroops
                }))}
                type="bar"
                icon={<FaSkullCrossbones className={`mr-2 ${theme === 'light' ? 'text-yellow-500' : 'text-yellow-400' }`} />}
                theme={theme}
              />
            </div>
          </section>

          {/* Top Performers - Redesigned */}
          <section className="mb-8">
            <div className="flex items-center mb-4">
                <FaUsers className={`h-6 w-6 mr-3 ${theme === 'light' ? 'text-indigo-600' : 'text-indigo-400'}`} />
                <h2 className={`text-2xl font-semibold ${theme === 'light' ? 'text-gray-800' : 'text-gray-200'}`}>
                    Top Player Gains
                </h2>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Power Growers */}
              <div className={`rounded-xl overflow-hidden shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
                <div className={`px-4 py-3 border-b ${theme === 'light' ? 'bg-slate-50 border-slate-200' : 'bg-gray-700/50 border-gray-600'}`}>
                  <h3 className={`font-semibold flex items-center ${theme === 'light' ? 'text-green-700' : 'text-green-400'}`}>
                    <FaArrowUp className="mr-2" /> Top Power Growers
                  </h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}">
                    <thead className={`${theme === 'light' ? 'bg-slate-50' : 'bg-gray-700/30'}`}>
                      <tr>
                        <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                          #
                        </th>
                        <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                          Player
                        </th>
                        <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                          Alliance
                        </th>
                        <th scope="col" className={`px-4 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                          Power Growth
                        </th>
                      </tr>
                    </thead>
                    <tbody className={`${theme === 'light' ? 'bg-white' : 'bg-gray-800'} divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}`}>
                      {[...performanceData.playerMetrics]
                        .sort((a, b) => b.powerGrowth - a.powerGrowth)
                        .slice(0, 10)
                        .map((player, index) => (
                          <tr key={player.playerId} className={`${index % 2 === 0 ? (theme === 'light' ? 'bg-white' : 'bg-gray-800') : (theme === 'light' ? 'bg-slate-50' : 'bg-gray-800/50')} hover:${theme === 'light' ? 'bg-slate-100' : 'bg-gray-700'} transition-colors`}>
                            <td className={`px-4 py-3 whitespace-nowrap text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                              {index + 1}
                            </td>
                            <td className={`px-4 py-3 whitespace-nowrap text-sm font-semibold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                              {player.playerName}
                            </td>
                            <td className={`px-4 py-3 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                              {player.alliance}
                            </td>
                            <td className={`px-4 py-3 whitespace-nowrap text-sm text-right font-bold ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>
                              +{formatNumber(player.powerGrowth)}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Top Killers */}
              <div className={`rounded-xl overflow-hidden shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
                <div className={`px-4 py-3 border-b ${theme === 'light' ? 'bg-slate-50 border-slate-200' : 'bg-gray-700/50 border-gray-600'}`}>
                  <h3 className={`font-semibold flex items-center ${theme === 'light' ? 'text-red-700' : 'text-red-400'}`}>
                    <FaFistRaised className="mr-2" /> Top Kill Point Gainers
                  </h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}">
                    <thead className={`${theme === 'light' ? 'bg-slate-50' : 'bg-gray-700/30'}`}>
                      <tr>
                        <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                          #
                        </th>
                        <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                          Player
                        </th>
                        <th scope="col" className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                          Alliance
                        </th>
                        <th scope="col" className={`px-4 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider`}>
                          Kill Points Gained
                        </th>
                      </tr>
                    </thead>
                    <tbody className={`${theme === 'light' ? 'bg-white' : 'bg-gray-800'} divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}`}>
                      {[...performanceData.playerMetrics]
                        .sort((a, b) => b.killPointsGrowth.total - a.killPointsGrowth.total)
                        .slice(0, 10)
                        .map((player, index) => (
                          <tr key={player.playerId} className={`${index % 2 === 0 ? (theme === 'light' ? 'bg-white' : 'bg-gray-800') : (theme === 'light' ? 'bg-slate-50' : 'bg-gray-800/50')} hover:${theme === 'light' ? 'bg-slate-100' : 'bg-gray-700'} transition-colors`}>
                            <td className={`px-4 py-3 whitespace-nowrap text-sm font-medium ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                              {index + 1}
                            </td>
                            <td className={`px-4 py-3 whitespace-nowrap text-sm font-semibold ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                              {player.playerName}
                            </td>
                            <td className={`px-4 py-3 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                              {player.alliance}
                            </td>
                            <td className={`px-4 py-3 whitespace-nowrap text-sm text-right font-bold ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>
                              +{formatNumber(player.killPointsGrowth.total)}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </section>
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FaRegSmileBeam className={`h-16 w-16 mb-4 ${theme === 'light' ? 'text-blue-500' : 'text-blue-400'}`} />
          <div className={`p-4 rounded-lg ${theme === 'light' ? 'bg-blue-50 text-blue-700' : 'bg-blue-900 text-blue-300'} max-w-lg`}>
            <h3 className="font-bold text-lg mb-2">Ready to Compare!</h3>
            <p className="text-sm">Please select two different scans above to compare player and alliance performance data.</p>
            <p className="mt-3 text-xs">
              If you've selected scans and see this, there might be no overlapping data or an issue. Check console for details.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScanComparisonPage;
