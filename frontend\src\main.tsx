import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { ThemeProvider } from './contexts/ThemeContext';
import GlobalErrorBoundary from './components/GlobalErrorBoundary';
// import loadData from './services/dataLoader'; // Removed as mock data is statically available

// Initialize data before rendering the app
console.log('[Main] Starting application...');
// const dataLoaded = loadData(); // Removed
// console.log('Data loaded:', dataLoaded); // Removed

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <GlobalErrorBoundary>
      <ThemeProvider>
        <App />
      </ThemeProvider>
    </GlobalErrorBoundary>
  </React.StrictMode>,
)